const path = require('path')
const fs = require('fs').promises
import logger from '../utils/logger'
// const grpc = require('@grpc/grpc-js')
import * as grpc from '@grpc/grpc-js'
const protoLoader = require('@grpc/proto-loader')
// 封装思路，获取存根
interface FsReaddir {
  // fs.readdir 的返回值类型,用到的属性
  name: string // 文件名称
  path: string // 文件路径
  isDirectory: () => boolean // 是否是目录
}
interface GrpcError {
  code: grpc.status
  message: string
  details?: string
}
interface GrpcCallOptions {
  timeout?: number
  metadata?: grpc.Metadata
  onData?: (data: any) => void
  onEnd?: () => void
  onError?: (error: GrpcError) => void
  onStatus?: (status: grpc.StatusObject) => void
}
interface MethodsData {
  fullServiceName: string
  requestStream: boolean
  responseStream: boolean
}
/**
 * 此类负责所有gRPC服务的管理，包括加载.proto文件、注册服务、获取客户端、调用服务等。
 */
class GrpcClientCore {
  // public async getStub() {
  //   const dirPath = path.join(__dirname, 'protos')
  //   // const protoFiles = await this.getAllProtoFiles(dirPath)
  // }
  private async getAllProtoFiles(dirPath: string) {
    let results: string[] = []
    try {
      const items: FsReaddir[] = await fs.readdir(dirPath, { withFileTypes: true })
      for (let i = 0; i < items.length; i++) {
        const item: FsReaddir = items[i]
        const fullPath: string = path.join(dirPath, item.name)
        if (item.isDirectory()) {
          results = results.concat(await this.getAllProtoFiles(fullPath))
        } else {
          results.push(fullPath)
        }
      }
    } catch (error) {
      logger.info(`Error reading directory ${dirPath}:${error}`)
    }
    return results
  }
  /**
   * 加载并解析.proto文件
   * @param {string[]} protoFiles .proto文件路径数组
   * @returns {Promise<Object>} gRPC包定义
   */
  private async loadProtoDefinitions(protoFiles) {
    if (protoFiles.length === 0) {
      throw new Error('No .proto files provided')
    }

    const includeDirs = [
      ...new Set(protoFiles.map((file) => path.dirname(file))), // 获取所有不重复的目录
    ]
    const options = {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs,
    }

    try {
      const packageDefinition = await protoLoader.load(protoFiles, options)
      return grpc.loadPackageDefinition(packageDefinition)
    } catch (error) {
      logger.info(`Error loading proto definitions:${error}`)
      throw error
    }
  }
  /**
   * 注册所有找到的gRPC服务
   * @param {Object} packageDefinition 从proto加载的包定义
   * @param {string} serverAddress 服务端地址，如'localhost:50051'
   */
  private services: any = {}
  private methodsData: Map<string, MethodsData> = new Map() // 方法的信息
  private DEFAULT_OPTIONS = {
    // 默认配置
    'grpc.max_send_message_length': 10 * 1024 * 1024, // 最大发送消息大小 10MB
    'grpc.max_receive_message_length': 10 * 1024 * 1024, // 最大接收消息大小 10MB
  }
  private registerAllServices(packageDefinition: any, serverAddress: any, options?: any) {
    // 清除现有服务
    this.services = {}
    this.methodsData.clear()

    // 遍历包定义中的所有服务
    for (const [packageName, packageContent] of Object.entries(packageDefinition)) {
      if (typeof packageContent !== 'object') continue
      for (const [serviceName, ServiceClient] of Object.entries(packageContent as any)) {
        // 检查是否为有效的服务客户端
        const serviceClient: any = ServiceClient
        if (serviceClient && serviceClient.service && typeof serviceClient.service === 'object') {
          const fullServiceName = `${packageName}.${serviceName}`
          let opts = this.DEFAULT_OPTIONS
          if (options) {
            opts = {
              ...this.DEFAULT_OPTIONS,
              ...options,
            }
          }
          this.services[fullServiceName] = new serviceClient(
            serverAddress,
            grpc.credentials.createInsecure(),
            opts,
          )
          for (const [methodName, method] of Object.entries(serviceClient.service)) {
            const md: any = method
            // 查看不同服务中的方法是否有重复
            const key: string = `${fullServiceName}.${methodName}`
            this.methodsData.set(key, {
              fullServiceName,
              requestStream: md.requestStream,
              responseStream: md.responseStream,
            })
          }
        }
      }
    }
  }
  /**
   * 获取指定服务的客户端
   * @param {string} serviceName 服务名称(格式: packageName.ServiceName)
   * @returns {Object} gRPC客户端实例
   */
  public getServiceClient(serviceName) {
    const client = this.services[serviceName]
    if (!client) {
      throw new Error(`Service ${serviceName} not found`)
    }
    return client
  }
  //
  /**
   * 使用这种方式，可以避免不同的服务中有相同的方法名称
   * @param {string} serviceName 服务名称(格式: packageName.ServiceName.methodName)
   */
  public getMethods(methodName) {
    // 防止在开发过程中出现不同服务中有同名方法的情况
    const service = this.methodsData.get(methodName) // 根据保存的 [存根.服务名.方法名] 获取保存的服务名称和方法信息
    if (!service) {
      throw new Error(`Method ${methodName} not found`)
    }
    const nameList = methodName.split('.')
    if (nameList.length !== 3) {
      throw new Error(`Invalid method name ${methodName}`)
    }
    const mName = nameList[2]
    const method = this.services[service.fullServiceName][mName]
    if (typeof method !== 'function') {
      throw new Error(`Method ${methodName} not found in service`)
    }
    return method
  }
  /**
   * 获取gprc客户端实例
   * @param methodName 方法名称
   * @returns client:服务实例, method:方法实例
   */
  public getClientByMethod(methodName) {
    const methodNames = [...this.methodsData.keys()]
    const methodsFilter = methodNames.filter((name) => name.includes(methodName))
    if (methodsFilter.length === 0) {
      throw new Error(`Method ${methodName} not found`)
    }
    if (methodsFilter.length > 1) {
      console.warn(`Multiple methods found for ${methodName}`)
    }
    const itemName = methodsFilter[0]
    const server = this.methodsData.get(itemName)
    const client = this.services[server?.fullServiceName || '']
    const method = client[methodName]
    if (typeof method !== 'function') {
      throw new Error(`Method ${methodName} not found in service`)
    }
    return {
      client,
      method,
    }
  }
  /**
   * 根据方法名称获取方法全名；如果有不同服务有相同的方法名，会按照顺序返回第一个匹配的
   * @param methodName 方法名称
   * @returns 该方法的信息，从而判断是具体那种通讯模式
   */
  public getMethodByName(methodName) {
    const methodNames = [...this.methodsData.keys()]
    const methodsFilter = methodNames.filter((name) => name.includes(methodName))
    if (methodsFilter.length === 0) {
      throw new Error(`Method ${methodName} not found`)
    }
    if (methodsFilter.length > 1) {
      console.warn(`Multiple methods found for ${methodName}`)
    }
    const itemName = methodsFilter[0]
    const methodData = this.methodsData.get(itemName)
    if (!methodData) {
      throw new Error(`Method ${itemName} not found in service`)
    }
    return methodData
  }
  // private packageDefinitions: any = {} // 存储加载的proto定义
  /**
   * 初始化gRPC服务管理器
   * @param {string} protoDir .proto文件所在目录
   * @param {string} serverAddress gRPC服务端地址
   */
  public async initialize(serverAddress: string, options?: Record<string, any>) {
    const dirPath = path.join(__dirname, 'protos') // proto文件所在目录
    try {
      // 1. 获取所有.proto文件
      const protoFiles = await this.getAllProtoFiles(dirPath)

      if (protoFiles.length === 0) {
        throw new Error('No .proto files found in the specified directory')
      }

      // 2. 加载proto定义
      const packageDefinition = await this.loadProtoDefinitions(protoFiles)
      // this.packageDefinitions = packageDefinition
      // 3. 注册所有服务
      this.registerAllServices(packageDefinition, serverAddress, options)

      return { success: true, services: Object.keys(this.services), options }
    } catch (error: any) {
      console.error('Initialization failed:', error)
      return { success: false, error: error.message }
    }
  }
}
export interface ClientOptions {
  serverAddress: string
  options?: Record<string, any>
}
export interface RequestInterceptor {
  allow: boolean
  serverAddress: string
  methodName: string
  serviceName: string
  request: any
  options: any
}
/**
 * 此类负责对外提供gRPC服务调用的接口
 */
class GrpcClient {
  // private static initOptions: ClientOptions
  // constructor() {
  //   // this.initialize(options.serverAddress, options.options)
  // }
  // 使用单例模式，避免重复创建客户端
  private DEFSULT_TIMEOUT = 30000 // 默认超时时间 10s
  private static instance: GrpcClient // serverAddress: string, options?: Record<string, any>
  public static getInstance(): GrpcClient {
    if (!GrpcClient.instance) {
      GrpcClient.instance = new GrpcClient()
    }
    return GrpcClient.instance
  }
  private grpcClient: GrpcClientCore = new GrpcClientCore()
  private grpcOption: ClientOptions | null = null
  public async initialize(options: ClientOptions): Promise<any> {
    this.grpcOption = options
    const result = await this.grpcClient.initialize(options.serverAddress, options.options)
    return result
  }
  private requestInterceptor?: (
    config: RequestInterceptor,
  ) => Promise<RequestInterceptor | boolean> | boolean | RequestInterceptor

  // 设置请求拦截器
  public setRequestInterceptor(
    interceptor: (
      config: RequestInterceptor,
    ) => Promise<RequestInterceptor | boolean> | boolean | RequestInterceptor,
  ) {
    this.requestInterceptor = interceptor
  }
  // 根据保存的存根信息，直接推断出客户端具体调用那些方法
  public async callMethods(methodName: string, request: any, options): Promise<any> {
    let actualRequest = request
    let actualOptions = options
    let actualServerAddress = this.grpcOption?.serverAddress || ''
    // 请求拦截器处理
    const methodData: MethodsData = this.grpcClient.getMethodByName(methodName)
    if (this.requestInterceptor) {
      const result: boolean | RequestInterceptor = await this.requestInterceptor({
        allow: true,
        methodName,
        serviceName: methodData.fullServiceName,
        serverAddress: this.grpcOption?.serverAddress || '',
        request,
        options,
      })
      if (typeof result !== 'boolean') {
        if (!result.allow) {
          throw new Error(`${methodName} request interceptor reject`)
        }
        if (result.request !== undefined) actualRequest = result.request
        if (result.options !== undefined) actualOptions = result.options
        if (result.serverAddress !== undefined) actualServerAddress = result.serverAddress
        // 如果需要切换服务器连接，可以重新初始化 grpcClient
        if (result.serverAddress && result.serverAddress !== this.grpcOption?.serverAddress) {
          await this.grpcClient.initialize(actualServerAddress, this.grpcOption?.options)
          if (this.grpcOption && this.grpcOption.serverAddress) {
            this.grpcOption.serverAddress = actualServerAddress
          }
        }
      } else {
        if (!result) {
          throw new Error(`${methodName} request interceptor reject`)
        }
      }
    }
    if (!methodData.requestStream && !methodData.responseStream) {
      // 无流式调用
      return this.unaryCall(methodName, actualRequest, actualOptions)
    } else if (!methodData.requestStream && methodData.responseStream) {
      // 服务器流式调用
      return this.serverStreaming(methodName, actualRequest, actualOptions)
    } else if (methodData.requestStream && !methodData.responseStream) {
      // 客户端流式调用
      return this.clientStreaming(methodName, actualOptions)
    } else {
      // 双向流式调用
      return this.bidirectionalStreaming(methodName, actualOptions)
    }
  }
  /**
   * 一元调用 (Unary)
   */
  public async unaryCall<TRequest, TResponse>(
    methodName: string,
    request: TRequest,
    options: Omit<GrpcCallOptions, 'onData' | 'onEnd' | 'onStatus' | 'onError'> = {},
  ): Promise<TResponse> {
    // const client = this.grpcClient.getServiceClient(serviceName)
    // const method = this.grpcClient.getMethods(serviceName + '.' + methodName)
    const { client, method } = this.grpcClient.getClientByMethod(methodName)
    return new Promise<TResponse>((resolve, reject) => {
      const call = method.call(
        client,
        request,
        options.metadata || new grpc.Metadata(),
        { deadline: this.getDeadline(options.timeout) },
        (error: grpc.ServiceError | null, response: TResponse) => {
          if (error) {
            reject(this.formatError(error))
          } else {
            resolve(response)
          }
        },
      )

      this.setupCallEvents(call, options)
    })
  }
  /**
   * 服务器流 (Server Streaming)
   */
  public serverStreaming<TRequest, TResponse>(
    methodName: string,
    request: TRequest,
    options: GrpcCallOptions = {},
  ): grpc.ClientReadableStream<TResponse> {
    // const client = this.grpcClient.getServiceClient(serviceName)
    // const method = this.grpcClient.getMethods(serviceName + '.' + methodName)
    const { client, method } = this.grpcClient.getClientByMethod(methodName)

    const call = method.call(client, request, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call
  }
  /**
  public static clientStreaming<TRequest, >(
   * 客户端流 (Client Streaming)
   */
  public clientStreaming<TRequest>(
    methodName: string,
    options: GrpcCallOptions = {},
  ): grpc.ClientWritableStream<TRequest> {
    // const client = this.grpcClient.getServiceClient(serviceName)
    // const method = this.grpcClient.getMethods(serviceName + '.' + methodName)
    const { client, method } = this.grpcClient.getClientByMethod(methodName)

    const call = method.call(client, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call as grpc.ClientWritableStream<TRequest>
  }
  /**
   * 双向流 (Bidirectional Streaming)
   */
  public bidirectionalStreaming<TRequest, TResponse>(
    methodName: string,
    options: GrpcCallOptions = {},
  ): grpc.ClientDuplexStream<TRequest, TResponse> {
    // const client = this.grpcClient.getServiceClient(serviceName)
    // const method = this.grpcClient.getMethods(serviceName + '.' + methodName)
    const { client, method } = this.grpcClient.getClientByMethod(methodName)

    const call = method.call(client, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call
  }
  /**
   * 处理请求事件
   * @param call grpc调用实例
   * @param options grpc调用选项
   */
  private setupCallEvents(
    call:
      | grpc.ClientReadableStream<any>
      | grpc.ClientWritableStream<any>
      | grpc.ClientDuplexStream<any, any>,
    options: GrpcCallOptions,
  ): void {
    if (options.timeout) {
      const timeoutId = setTimeout(() => {
        if (!call.destroyed) {
          call.cancel()
        }
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
      }, options.timeout)
    }

    call.on('data', (data: any) => {
      if (options.onData) options.onData(data)
    })

    call.on('end', () => {
      if (options.onEnd) options.onEnd()
    })

    call.on('error', (error: grpc.ServiceError) => {
      if (options.onError) options.onError(this.formatError(error))
    })

    call.on('status', (status: grpc.StatusObject) => {
      if (options.onStatus) options.onStatus(status)
    })
  }
  private formatError(error: grpc.ServiceError): GrpcError {
    return {
      code: error.code || grpc.status.UNKNOWN,
      message: error.message,
      details: error.details,
    }
  }

  private getDeadline(timeoutMs?: number): Date | undefined {
    const defaultTimeout = this.DEFSULT_TIMEOUT
    const timeoutMsOrDefault = timeoutMs || defaultTimeout
    return timeoutMsOrDefault ? new Date(Date.now() + timeoutMsOrDefault) : undefined
  }
}

export const grpcClient = GrpcClient.getInstance()
