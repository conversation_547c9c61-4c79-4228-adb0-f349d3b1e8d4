import { electronApp, optimizer } from '@electron-toolkit/utils'
import { app, BrowserWindow, globalShortcut } from 'electron'
// import GrpcManager from './grpc/grpcManager'
import { LinkerClient } from './grpc/linkerClient'
import { LinkerServer } from './grpc/linkerServer'
import { RpcMgr } from './grpc/rpcMgr'
import { setupIpcHandlers } from './ipc'
import { configService } from './services/configService'
import { createWorkflowService } from './services/workflowService'
import logger from './utils/logger'
import { createMainWindow } from './window/mainWindow'
// import { threadPool } from './workers/core/WorkerPoolManager'

//  RpcMgr 实例
let rpcMgr: RpcMgr | null = null
//  LinkerClient 实例
const linkerClient: LinkerClient | null = null
// LinkerServer 实例
const linkerServer: LinkerServer | null = null
// 工作流服务实例
let workflowService: any = null
// 应用程序就绪
app.whenReady().then(async () => {
  // logger.info('CommandOrControl+A pressed------>>>>>>>>>>>>>>')

  // globalShortcut.register('CommandOrControl+A', () => {
  //   logger.info('CommandOrControl+A pressed------>>>>>>>>>>>>>>')
  //   const linkerApi =
  //     configService.getLinkerApiFromConfig() || process.env.VITE_APP_LINKER_API || ''
  //   grpcClient.initialize(linkerApi)
  // })
  // 设置应用ID
  const isMattIcon = process.env.VITE_APP_IS_MATT === '1'
  const appId = isMattIcon ? 'com.mattverse.app' : 'com.highpower.app'
  app.setAppUserModelId(appId)
  electronApp.setAppUserModelId(appId)

  // 初始化 RpcMgr
  try {
    const linkerApi =
      configService.getLinkerApiFromConfig() || process.env.VITE_APP_LINKER_API || ''
    const user_id = process.env.VITE_APP_USER_ID || ''
    const token = process.env.VITE_APP_USER_TOKEN || ''
    // const grpcManager = new GrpcManager()
    // const grpcClient = await grpcManager.initGrpcClient({
    //   serverAddress: linkerApi,
    // })
    // logger.info('注册存根上面所有的服务 GrpcClient initialized:------>>>', grpcClient)
    // const grpcClient = GrpcClient.getInstance()
    // const grpcResult = await grpcClient.initialize({
    //   serverAddress: linkerApi,
    // })
    // logger.info('注册存根上面所有的服务 GrpcClient initialized:------<>>>', grpcResult)
    // grpcClient.callMethods('getClientUrl', {}, {}).then((res: any) => {
    //   console.log('定时更新客户端URL:新grpcManager--->>>', res)
    // })
    logger.info(`初始化 RpcMgr: linkerApi=${linkerApi}, user_id=${user_id}`)
    rpcMgr = new RpcMgr(linkerApi, user_id, token)
    logger.info('RpcMgr initialized: linker url', linkerApi)

    // 初始化 LinkerClient
    // linkerClient = new LinkerClient()
    // linkerClient.setServerUrl(linkerApi)
    // linkerClient.setAuthInfo(user_id, token)
    // linkerClient.setPort('50051')

    // // 监听心跳事件
    // linkerClient.on('heartbeat-failed', (error) => {
    //   logger.warn(`心跳失败: ${error.message}`)
    // })

    // linkerClient.on('heartbeat-success', () => {
    //   logger.info('心跳成功------------')
    // })

    // linkerClient.on('max-reconnect-attempts-reached', () => {
    //   logger.error('已达到最大重连次数，请检查网络或服务器状态')
    // })

    // 创建工作流服务实例
    workflowService = createWorkflowService(rpcMgr)

    // 将工作流服务添加到 LinkerClient 服务列表中
    // linkerClient.setServices({ workflowService })

    // 初始化 LinkerServer
    // linkerServer = new LinkerServer(linkerClient, '50051')

    // 启动 LinkerServer
    // await linkerServer.start()

    // 获取client_url服务地址
    try {
      rpcMgr.call('getClientUrl', {}, (error, response) => {
        if (error) {
          logger.error('获取客户端URL失败:', error)
          return
        }

        if (response && response.client_url) {
          logger.info(`获取到客户端URL: ${response.client_url}`)
          if (linkerClient) {
            // linkerClient.setServerUrl(response.client_url)
          }
        } else {
          logger.warn('获取客户端URL失败: 响应中没有client_url字段')
        }
      })
    } catch (error) {
      logger.error('调用getClientUrl失败:', error)
    }
    // const grpcManager = new GrpcManager()
    // 添加定时更新URL的功能
    setInterval(() => {
      // const params: any = {
      //   user_id: user_id,
      //   token,
      // }
      // metadata.add('method', 'getClientUrl')
      // metadata.add('source', 'client')
      // grpcManager.call('getClientUrl', params, metadata).then((res: any) => {
      //   logger.info('定时更新客户端URL:新grpcManager--->>>', res)
      // })
      if (rpcMgr && linkerClient) {
        rpcMgr.call('getClientUrl', {}, (error, response) => {
          if (error) {
            logger.error('定时更新客户端URL失败:', error)
            return
          }

          if (response && response.client_url) {
            logger.info(`定时更新客户端URL: ${response.client_url}`)
            if (linkerClient) {
              // linkerClient.setServerUrl(response.client_url)
            }
          }
        })
      }
    }, 60000) //60000
  } catch (error) {
    logger.error('RpcMgr init-error:', error)
  }

  // 设置IPC处理程序 - 这里会设置所有IPC处理程序，包括窗口控制
  setupIpcHandlers(rpcMgr)

  // 创建主窗口
  createMainWindow()

  // 注册F12快捷键打开开发者工具
  try {
    const f12Registered = globalShortcut.register('F12', () => {
      const win = BrowserWindow.getFocusedWindow()
      if (win) {
        win.webContents.toggleDevTools()
        logger.info('F12 pressed - toggled developer tools')
      }
    })

    if (!f12Registered) {
      logger.warn('F12快捷键注册失败，尝试注册替代快捷键')
      // 尝试注册替代快捷键
      const altF12Registered = globalShortcut.register('Alt+F12', () => {
        const win = BrowserWindow.getFocusedWindow()
        if (win) {
          win.webContents.toggleDevTools()
          logger.info('Alt+F12 pressed - toggled developer tools')
        }
      })

      if (altF12Registered) {
        logger.info('Alt+F12快捷键注册成功，可使用Alt+F12打开开发者工具')
      }
    } else {
      logger.info('F12快捷键注册成功')
    }
  } catch (error) {
    logger.error('注册F12快捷键时出错:', error)
  }
  // 注册Vue Devtools快捷键
  globalShortcut.register('CommandOrControl+Shift+I', () => {
    const win = BrowserWindow.getFocusedWindow()
    if (win) {
      win.webContents.toggleDevTools()
      logger.info('DevTools shortcut pressed - toggled developer tools')
    }
  })

  // 注册工作流服务到中台
  await registerWorkflowService(rpcMgr)

  // 监控窗口快捷键
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })
})
// 应用退出时清理
// app.on('before-quit', async () => {
//   await threadPool.destroy()
// })
/**
 * 注册工作流服务到中台
 * @param rpcMgr RPC管理器实例
 */
async function registerWorkflowService(rpcMgr: RpcMgr | null): Promise<void> {
  try {
    if (!rpcMgr) {
      logger.error('RpcMgr未初始化，无法注册工作流服务')
      return
    }

    if (!linkerClient) {
      logger.error('LinkerClient未初始化，无法注册工作流服务')
      return
    }

    if (!workflowService) {
      logger.error('workflowService未初始化，无法注册工作流服务')
      return
    }

    // 检查 workflowService 是否有 getServicesConfig 方法
    if (typeof workflowService.getServicesConfig !== 'function') {
      logger.error('workflowService 缺少 getServicesConfig 方法')
      return
    }

    // 注册本地服务到中台
    try {
      await linkerClient.register()
      logger.info('本地服务已成功注册到中台')
    } catch (error) {
      logger.error('本地服务注册失败:', error)
    }

    // 注册工作流服务
    const servicesConfig = workflowService.getServicesConfig()
    if (!servicesConfig) {
      logger.error('获取服务配置失败')
      return
    }

    rpcMgr.call(
      'register',
      {
        service_name: 'workflowService',
        service_name_list: servicesConfig.service_name_list,
        service_version_list: servicesConfig.service_version_list,
        service_access_level_list: servicesConfig.service_access_level_list,
      },
      (error, response) => {
        if (error) {
          logger.error('工作流服务注册调用失败:', error)
          return
        }

        if (response && response.status === 'Success') {
          logger.info('工作流服务注册成功:', response)
        } else {
          logger.error('工作流服务注册失败:', response)
        }
      },
    )
  } catch (error) {
    logger.error('注册工作流服务时出错:', error)
  }
}
// 应用关闭时清理
// app.on('before-quit', async () => {
//   await threadPool?.shutdown()
// })
// 处理窗口关闭事件
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// macOS 应用激活处理
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow()
  }
})
// 应用退出前注销所有快捷键
app.on('will-quit', () => {
  logger.info('应用即将退出，注销所有快捷键')
  globalShortcut.unregisterAll()
})
