import { ClientOptions, grpcClient } from './grpcClient'
const grpc = require('@grpc/grpc-js')
class GrpcManager {
  public async initGrpcClient(options: ClientOptions) {
    // 添加请求拦截器
    // grpcClient.setRequestInterceptor(async (config) => {
    //   return config
    // })
    return await grpcClient.initialize(options)
  }
  // 获取用户信息
  private getUserInfo() {
    const userId = process.env.VITE_APP_USER_ID || ''
    const token = process.env.VITE_APP_USER_TOKEN || ''
    return { userId, token }
  }
  private getMataData(apiName: string) {
    return {
      method: apiName,
      source: 'client',
    }
  }
  // 通用调用方法
  public call(apiName, params?: any, metadata?: any, option?: any) {
    let grpcOption = {}
    let grpcMeta = {}
    // 添加默认的metadata
    const defaultMetaData = this.getMataData(apiName)
    if (defaultMetaData) {
      grpcMeta = { ...defaultMetaData, ...metadata }
    }
    if (metadata) {
      // 处理metadata
      grpcMeta = { ...grpcMeta, ...metadata }
      grpcOption = { metadata: this.objToMateData(grpcMeta), ...option }
    }
    const grpcParams = {}
    //
    const userData = this.getUserInfo()
    if (userData.userId) {
      grpcParams['userId'] = userData.userId
    }
    if (userData.token) {
      grpcParams['token'] = userData.token
    }
    if (params) {
      // 处理params
      for (const key of params) {
        if (this.hasOwn(params, key)) {
          grpcParams[key] = params[key]
        }
      }
    }
    if (option && Object.keys(option).length > 0) {
      grpcOption = { ...metadata, ...option }
    }
    return grpcClient.callMethods(apiName, grpcParams, grpcOption)
  }
  private hasOwn(obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop)
  }
  private objToMateData(obj) {
    const metaData = new grpc.Metadata()
    for (const key in obj) {
      if (this.hasOwn(obj, key)) {
        metaData.add(key, obj[key])
      }
    }
    return metaData
  }
  // private beforeCall(apiName, params, metadata, option) {
  //   // 处理beforeCall
  // }
  // private afterCall(apiName, params, metadata, option, result) {
  //   // 处理afterCall
  // }
}
export default GrpcManager
