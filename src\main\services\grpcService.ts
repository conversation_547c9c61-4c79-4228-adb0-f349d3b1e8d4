import fs from 'fs'
import { RpcMgr } from '../grpc/rpcMgr'
import logger from '../utils/logger'
import { getConfigPath } from '../utils/paths'
/**
 * gRPC服务类
 * 封装与gRPC相关的所有操作
 */
export class GrpcService {
  private rpcMgr: RpcMgr | null = null
  private streamControllers: Map<string, AbortController> = new Map()
  /**
   * 初始化gRPC服务
   * @param rpcMgr RpcMgr实例
   */
  constructor(rpcMgr: RpcMgr | null) {
    this.rpcMgr = rpcMgr
  }

  /**
   * 调用gRPC API
   * @param apiName API名称
   * @param params 参数
   * @returns Promise<any>
   */
  async call(apiName: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        // logger.info(`gRPC call 调用: ${apiName}`, params)

        if (!this.rpcMgr) {
          throw new Error('RpcMgr 未初始化')
        }

        this.rpcMgr.call(apiName, params, (error, response) => {
          if (error) {
            reject(error)
          } else {
            resolve(response)
          }
        })
      } catch (error) {
        logger.error(`gRPC call 错误 (${apiName}):`, error)
        reject(error)
      }
    })
  }

  // grpcService
  /**
   * 提交gRPC同步任务
   * @param serviceName 服务名称
   * @param serverId 服务器ID
   * @param isSave 是否保存
   * @param params 参数
   * @returns Promise<any>
   */
  async default(serviceName: string, serverId: string, isSave: boolean, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        logger.info(`gRPC 提交同步任务: ${serviceName}`, params)
        if (!this.rpcMgr) {
          throw new Error('RpcMgr 未初始化')
        }

        this.rpcMgr.default(serviceName, serverId, isSave, params, (error, response) => {
          if (error) {
            reject(error)
          } else {
            resolve(response)
          }
        })
      } catch (error) {
        logger.error(`gRPC 提交同步任务失败 (${serviceName}):`, error)
        reject(error)
      }
    })
  }

  /**
   * 提交gRPC计算任务
   * @param serviceName 服务名称
   * @param serverId 服务器ID
   * @param isSave 是否保存
   * @param params 参数
   * @returns Promise<any>
   */
  async submit(serviceName: string, serverId: string, isSave: boolean, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      try {
        logger.info(`gRPC 提交计算任务: ${serviceName}`, params)

        if (!this.rpcMgr) {
          throw new Error('RpcMgr 未初始化')
        }

        this.rpcMgr.submit(serviceName, serverId, isSave, params, (error, response) => {
          if (error) {
            reject(error)
          } else {
            resolve(response)
          }
        })
      } catch (error) {
        logger.error(`gRPC 提交计算任务失败 (${serviceName}):`, error)
        reject(error)
      }
    })
  }

  /**
   * 调用gRPC Agent
   * @param serviceName 服务名称
   * @param serverId 服务器ID
   * @param sessionId 会话ID
   * @param modelType 模型类型
   * @param isStreamResponse 是否流式响应
   * @param messages 消息列表
   * @param callback 回调函数
   */
  agent(
    serviceName: string,
    serverId: string,
    sessionId: string,
    modelType: string,
    isStreamResponse: boolean,
    messages: any,
    callback: (result: any, type: string) => void,
  ): void {
    try {
      logger.info(`调用Agent服务: ${serviceName}, 会话ID: ${sessionId}`)

      if (!this.rpcMgr) {
        throw new Error('RpcMgr 未初始化')
      }

      // 如果已存在该会话的控制器，先取消它
      if (this.streamControllers.has(sessionId)) {
        logger.info(`取消会话 ${sessionId} 的现有流式响应`)
        this.streamControllers.get(sessionId)?.abort()
        this.streamControllers.delete(sessionId)
      }

      // 创建新的AbortController
      const controller = new AbortController()
      this.streamControllers.set(sessionId, controller)

      // 调用RPC服务
      this.rpcMgr.agent(
        serviceName,
        serverId,
        sessionId,
        modelType,
        isStreamResponse,
        messages,
        (result: any, type: string) => {
          callback(result, type)

          // 如果是结束或错误，清理控制器
          if (type === 'end' || type === 'error') {
            this.streamControllers.delete(sessionId)
          }
        },
        controller.signal, // 传递AbortSignal
      )
    } catch (error) {
      logger.error(`Agent服务调用失败: ${error}`)
      callback({ message: `调用失败: ${error}` }, 'error')
      this.streamControllers.delete(sessionId)
    }
  }

  /**
   * 调用gRPC setClient，建立长连接
   * @param userName 用户名
   * @param loginIp 登录IP
   * @param password 密码
   * @param expiredTime 过期时间
   * @param callback 回调函数，用于处理流式响应
   */
  setClient(
    userName: string,
    loginIp: string,
    password: string,
    expiredTime: number,
    callback?: (result: any, type: string) => void,
  ): void {
    try {
      logger.info(`gRPC setClient 调用: ${userName}`, { loginIp, expiredTime })

      if (!this.rpcMgr) {
        if (callback) {
          callback({ message: 'RpcMgr 未初始化' }, 'error')
        }
        return
      }

      this.rpcMgr.setClient(userName, loginIp, password, expiredTime, (data, type) => {
        logger.debug('处理数据=========data=========', data)
        if (!data) {
          // 连接结束
          if (type === 'end') {
            logger.info('setClientChannel 连接已结束')
            if (callback) {
              callback(undefined, 'end')
            }
          } else if (type === 'error') {
            logger.error('setClient 错误:', data)
            if (callback) {
              callback(data, 'error')
            }
          }
          return
        }

        // 根据消息类型进行不同处理
        if (data.message === 'Ping') {
          logger.debug('收到心跳消息')
          if (callback) {
            callback(data, 'data')
          }
        } else if (data.message === 'Channel established') {
          logger.info('连接已建立')
          if (callback) {
            callback(data, 'data')
          }
        } else if (data.key_value_pairs && data.key_value_pairs.function_name) {
          // 功能调用消息
          logger.info(`收到功能调用: ${data.key_value_pairs.function_name}`, JSON.stringify(data))
          if (callback) {
            callback(data, 'data')
          }
        } else if (type === 'error') {
          logger.error('setClient 错误:', data)
          if (callback) {
            callback(data, 'error')
          }
        } else {
          // 其他数据消息
          logger.info(`收到其他数据:`, JSON.stringify(data))
          if (callback) {
            callback(data, 'data')
          }
        }
      })
    } catch (error) {
      logger.error(`gRPC setClient 错误:`, error)
      if (callback) {
        callback({ message: `setClient 错误: ${(error as Error).message}` }, 'error')
      }
    }
  }
  streamUpload: any = null
  /**
   * 链接uploadFile服务,初始化参数，并且多次调用的时候将参数调用到grpcService.uploadFile
   * 目前按照这种方式进行传值，还需要进一步优化
   * @param file_name 文件名
   * @param sha256 文件sha256
   * @param chunk_id 分块ID
   * @param content 文件内容
   * @param is_last 是否最后一块
   * @param callback 回调函数
   */
  clientSideStream(
    fileName: string,
    sha256: string,
    chunkId: number,
    content: number,
    isLast: false,
    callback?: any,
  ) {
    if (!this.rpcMgr) {
      if (callback) {
        callback({ message: 'RpcMgr 未初始化' }, 'error')
      }
      return
    }
    const params: any = {
      // 后续控制一下类型
      file_name: fileName,
      total_sha256: sha256,
      chunk_id: chunkId,
      content: content,
      is_last: isLast,
    }
    const usrId: any = this.rpcMgr.getUsers()
    params.user_id = usrId
    if (!this.streamUpload) {
      this.streamUpload = this.rpcMgr.clientSideStream((data, type) => {
        if (type === 'data') {
          if (callback) {
            logger.info(`上传结果: ${fileName}`)
            callback(data, 'data')
          }
        } else if (type === 'end') {
          if (callback) {
            callback(undefined, 'end')
          }
        } else {
          if (callback) {
            callback(data, 'error')
          }
        }
      })
      this.streamUpload.write(params)
      logger.info(`开始上传文件: ${fileName}，分块ID: ${chunkId}`)
    } else {
      this.streamUpload.write(params)
      logger.info(`推送文件块: ${fileName}，分块ID: ${chunkId}`)
    }
    if (isLast) {
      this.streamUpload = null
      logger.info(`文件上传完成: ${fileName}`)
      this.streamUpload = null
    }
  }

  /**
   * 获取连接状态
   * @returns {connected: boolean, linkerUrl: string}
   */
  getStatus(): { connected: boolean; linkerUrl: string } {
    if (!this.rpcMgr) {
      try {
        const configPath = getConfigPath()
        if (fs.existsSync(configPath)) {
          const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'))
          return {
            connected: false,
            linkerUrl: configData.linkerApi || process.env.VITE_APP_LINKER_API || '',
          }
        }
      } catch (error) {
        logger.error('读取配置文件失败:', error)
      }

      return { connected: false, linkerUrl: process.env.VITE_APP_LINKER_API || '' }
    }

    return {
      connected: this.rpcMgr.isConnected(),
      linkerUrl: this.rpcMgr.getLinkerUrl(),
    }
  }

  /**
   * 获取RpcMgr实例
   * @returns RpcMgr实例
   */
  getRpcMgr(): RpcMgr | null {
    return this.rpcMgr
  }

  /**
   * 设置RpcMgr实例
   * @param rpcMgr RpcMgr实例
   */
  setRpcMgr(rpcMgr: RpcMgr | null): void {
    this.rpcMgr = rpcMgr
  }

  /**
   * 更新认证信息
   * @param userId 用户ID
   * @param token 认证令牌
   * @returns {success: boolean, message: string}
   */
  updateAuthInfo(userId: string, token: string): { success: boolean; message: string } {
    try {
      if (!this.rpcMgr) {
        return { success: false, message: 'RpcMgr 未初始化' }
      }

      const updated = this.rpcMgr.updateAuthInfo(userId, token)
      if (updated) {
        logger.info(`GrpcService: 认证信息已更新 userId=${userId}`)
        return { success: true, message: '认证信息已更新' }
      } else {
        return { success: false, message: '认证信息更新失败：无效的参数' }
      }
    } catch (error) {
      logger.error('更新认证信息失败:', error)
      return { success: false, message: `更新失败: ${(error as Error).message}` }
    }
  }
  /**
   * 下载文件 - 服务端流式传输
   * @param storedFilepath 存储的文件路径
   * @param chunkSize 分块大小，默认1MB
   * @param callback 回调函数，用于处理流式响应
   * @returns 流对象或null
   */
  downloadFile(
    storedFilepath: string,
    chunkSize: number = 1024 * 1024,
    callback?: (result: any, type: string) => void,
  ): any {
    try {
      logger.info(`gRPC downloadFile 调用: ${storedFilepath}`, { chunkSize })

      if (!this.rpcMgr) {
        const error = new Error('RpcMgr 未初始化')
        if (callback) {
          callback(error, 'error')
        }
        return null
      }

      return this.rpcMgr.downloadFile(storedFilepath, chunkSize, (data, type) => {
        if (type === 'data') {
          // 处理文件块数据
          if (callback) {
            callback(data, 'data')
          }
        } else if (type === 'end') {
          // 下载完成
          logger.info(`文件下载完成: ${storedFilepath}`)
          if (callback) {
            callback(undefined, 'end')
          }
        } else if (type === 'error') {
          // 下载错误
          logger.error(`文件下载错误: ${storedFilepath}`, data)
          if (callback) {
            callback(data, 'error')
          }
        }
      })
    } catch (error) {
      logger.error(`gRPC downloadFile 错误:`, error)
      if (callback) {
        callback(error, 'error')
      }
      return null
    }
  }

  /**
   * 停止消息生成
   * @param sessionId 会话ID
   */
  stopGeneration(sessionId: string): void {
    try {
      logger.info(`尝试停止会话 ${sessionId} 的消息生成`)

      // 获取与该会话关联的流式响应控制器
      const controller = this.streamControllers.get(sessionId)
      if (controller) {
        // 取消流式响应
        controller.abort()
        this.streamControllers.delete(sessionId)
        logger.info(`已停止会话 ${sessionId} 的消息生成`)
      } else {
        logger.warn(`未找到会话 ${sessionId} 的流式响应控制器`)
      }
    } catch (error) {
      logger.error(`停止消息生成失败: ${error}`)
    }
  }
}

// 导出创建gRPC服务的工厂函数
export function createGrpcService(rpcMgr: RpcMgr | null): GrpcService {
  return new GrpcService(rpcMgr)
}
